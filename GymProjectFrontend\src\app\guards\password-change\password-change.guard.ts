import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { CanActivate, Router, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { isPlatformBrowser } from '@angular/common';

@Injectable({
  providedIn: 'root'
})
export class PasswordChangeGuard implements CanActivate {
  private isBrowser: boolean;

  constructor(
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
  }

  canActivate(): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    // If not in browser, allow server-side rendering to proceed
    if (!this.isBrowser) {
      return true;
    }

    const requirePasswordChange = localStorage.getItem('requirePasswordChange');

    if (requirePasswordChange === 'true') {
      // <PERSON><PERSON><PERSON> de<PERSON>rme zorunluluğu var<PERSON>, ş<PERSON><PERSON> de<PERSON>rme sayfasına yönlendir
      return this.router.createUrlTree(['/change-password']);
    }

    // <PERSON><PERSON><PERSON> de<PERSON>rme zorunluluğu yoksa, sayfaya erişime izin ver
    return true;
  }
}